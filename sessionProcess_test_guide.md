# sessionProcess 版本测试指南

## 📋 测试版本概览

| 版本 | 文件名 | 主要特点 | 测试重点 |
|------|--------|----------|----------|
| **v1** | `sessionProcess_v1_performance.js` | 性能优化版 | CPU/内存使用，高并发性能 |
| **v2** | `sessionProcess_v2_error_handling.js` | 错误处理增强版 | 错误日志，调试信息 |
| **v3** | `sessionProcess_v3_retry_optimization.js` | 连接重试优化版 | 连接稳定性，重试机制 |
| **v4** | `sessionProcess_v4_minimal.js` | 极简版 (ReadableStream) | 代码简洁性，基础功能 |
| **v4b** | `sessionProcess_v4b_minimal_transform.js` | 极简版 (TransformStream) | TransformStream 方式对比 |
| **v4c** | `sessionProcess_v4c_minimal_transform_enhanced.js` | 极简版 (TransformStream 增强) | 更接近原版本结构 |

## 🔄 按顺序测试步骤

### 测试版本1：性能优化版

1. **替换函数**：
   ```javascript
   // 在 _worker.js 中找到 sessionProcess 函数
   // 将其替换为 sessionProcess_v1_performance 的内容
   // 或者导入并重命名：
   import { sessionProcess_v1_performance as sessionProcess } from './sessionProcess_v1_performance.js';
   ```

2. **测试重点**：
   - 高并发连接测试
   - 内存使用监控
   - CPU 使用率观察
   - 连接建立速度

3. **预期效果**：
   - 更快的连接建立
   - 更低的内存占用
   - 更少的 CPU 开销

### 测试版本2：错误处理增强版

1. **替换函数**：
   ```javascript
   import { sessionProcess_v2_error_handling as sessionProcess } from './sessionProcess_v2_error_handling.js';
   ```

2. **测试重点**：
   - 查看控制台日志输出
   - 测试各种错误场景
   - 观察错误恢复能力
   - 检查资源清理情况

3. **预期效果**：
   - 详细的连接日志
   - 清晰的错误分类
   - 更好的问题定位能力

### 测试版本3：连接重试优化版

1. **替换函数**：
   ```javascript
   import { sessionProcess_v3_retry_optimization as sessionProcess } from './sessionProcess_v3_retry_optimization.js';
   ```

2. **测试重点**：
   - 网络不稳定环境测试
   - 连接超时处理
   - 重试机制效果
   - 连接成功率统计

3. **预期效果**：
   - 更高的连接成功率
   - 智能的重试策略
   - 更好的网络适应性

### 测试版本4：极简版 (ReadableStream)

1. **替换函数**：
   ```javascript
   import { sessionProcess_v4_minimal as sessionProcess } from './sessionProcess_v4_minimal.js';
   ```

2. **测试重点**：
   - 基础功能完整性
   - 代码可读性
   - 维护便利性
   - 核心功能稳定性

3. **预期效果**：
   - 代码最简洁
   - 逻辑最清晰
   - 维护成本最低

### 测试版本4b：极简版 (TransformStream)

1. **替换函数**：
   ```javascript
   import { sessionProcess_v4b_minimal_transform as sessionProcess } from './sessionProcess_v4b_minimal_transform.js';
   ```

2. **测试重点**：
   - 与版本4的功能对比
   - TransformStream vs ReadableStream 性能
   - 流处理稳定性
   - 内存使用对比

3. **预期效果**：
   - 功能与版本4相同
   - 可能有不同的性能特征
   - 更好的流控制

### 测试版本4c：极简版 (TransformStream 增强)

1. **替换函数**：
   ```javascript
   import { sessionProcess_v4c_minimal_transform_enhanced as sessionProcess } from './sessionProcess_v4c_minimal_transform_enhanced.js';
   ```

2. **测试重点**：
   - 更接近原版本的处理逻辑
   - Reader 模式性能优势
   - 错误处理完整性
   - 与原版本的兼容性

3. **预期效果**：
   - 保持极简的同时更稳定
   - 更好的错误处理
   - 性能接近原版本

## 🧪 测试方法建议

### 1. 功能测试
- 基础连接测试
- 数据传输测试
- 协议兼容性测试
- 错误场景测试

### 2. 性能测试
- 并发连接数测试
- 内存使用监控
- CPU 使用率测试
- 响应时间测试

### 3. 稳定性测试
- 长时间运行测试
- 网络中断恢复测试
- 异常情况处理测试
- 资源泄漏检查

## 📊 测试记录模板

```
版本：v1_performance
测试时间：____
测试环境：____
测试结果：
- 功能正常：✅/❌
- 性能表现：____
- 错误情况：____
- 其他观察：____
```

## 🔧 快速切换方法

在 `_worker.js` 中修改 `HANDLER_CHOICE` 配置：

```javascript
// 原代码中找到这一行：
const HANDLER_CHOICE = 5;

// 然后在 handlerConfigs 中添加新版本：
const handlerConfigs = {
    1: { sessionA: handleSession, sessionZ: handleSession },
    2: { sessionA: processSession, sessionZ: processSession },
    3: { sessionA: handleSession, sessionZ: processSession },
    4: { sessionA: sessionHandle, sessionZ: sessionHandle },
    5: { sessionA: sessionProcess, sessionZ: sessionProcess },
    6: { sessionA: sessionHandle, sessionZ: sessionProcess },
    // 添加新版本
    11: { sessionA: sessionProcess_v1_performance, sessionZ: sessionProcess_v1_performance },
    12: { sessionA: sessionProcess_v2_error_handling, sessionZ: sessionProcess_v2_error_handling },
    13: { sessionA: sessionProcess_v3_retry_optimization, sessionZ: sessionProcess_v3_retry_optimization },
    14: { sessionA: sessionProcess_v4_minimal, sessionZ: sessionProcess_v4_minimal },
    15: { sessionA: sessionProcess_v4b_minimal_transform, sessionZ: sessionProcess_v4b_minimal_transform },
    16: { sessionA: sessionProcess_v4c_minimal_transform_enhanced, sessionZ: sessionProcess_v4c_minimal_transform_enhanced },
};

// 修改 HANDLER_CHOICE 来切换版本：
const HANDLER_CHOICE = 11; // 测试 v1
// const HANDLER_CHOICE = 12; // 测试 v2
// const HANDLER_CHOICE = 13; // 测试 v3
// const HANDLER_CHOICE = 14; // 测试 v4 (ReadableStream)
// const HANDLER_CHOICE = 15; // 测试 v4b (TransformStream)
// const HANDLER_CHOICE = 16; // 测试 v4c (TransformStream 增强)
```

## 📝 注意事项

1. **备份原文件**：测试前请备份原始的 `_worker.js` 文件
2. **逐个测试**：建议一次只测试一个版本，避免混淆
3. **记录结果**：详细记录每个版本的测试结果
4. **环境一致**：确保测试环境一致，便于对比
5. **充分测试**：每个版本都要进行充分的功能和性能测试

开始测试版本1吧！🚀
