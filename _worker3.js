//订阅地址：https://域名/ID/vless .
import{connect as c}from"cloudflare:sockets";const a={
	id:"123abc",
	uuid:"0bae250b-cf05-459b-9e90-5d0b26b30475",
	node:"ns5.cloudflare.com",
	enableProxy:!1,// !0 → true（逻辑非 0 为 true，开启），!1 → false（逻辑非 1 为 false，关闭）; (反代开关)
	proxyIP:"ProxyIP.US.CMLiussss.net:443",//反代:地址/端口
	enableSocks5Proxy:!0,// !0 → true（逻辑非 0 为 true，开启），!1 → false（逻辑非 1 为 false，关闭）; (socks5开关)
	enableSocks5Global:!0,//!0 → true（逻辑非 0 为 true，开启），!1 → false（逻辑非 1 为 false，关闭）; (全局 SOCKS5开关)
	socks5Account:"lzj:<EMAIL>:14041", // SOCKS5 帐号，格式：用户名:密码@host:port  （感谢辣子鸡的socks5）
	nodeName:"天书tg版"};

export default{async fetch(e){const t=new URL(e.url),r=e.headers.get("Upgrade");a.id=e.env?.ID||a.id,a.uuid=e.env?.UUID||a.uuid,a.node=e.env?.NODE||a.node,a.proxyIP=e.env?.PROXYIP||a.proxyIP,a.socks5Account=e.env?.SOCKS5||a.socks5Account,a.enableSocks5Proxy="true"===e.env?.SOCKS5OPEN?!0:"false"===e.env?.SOCKS5OPEN?!1:a.enableSocks5Proxy,a.enableSocks5Global="true"===e.env?.SOCKS5GLOBAL?!0:"false"===e.env?.SOCKS5GLOBAL?!1:a.enableSocks5Global,console.log("配置:",JSON.stringify({enableProxy:a.enableProxy,enableSocks5Proxy:a.enableSocks5Proxy,enableSocks5Global:a.enableSocks5Global,socks5Account:a.socks5Account,proxyIP:a.proxyIP}));if("websocket"!==r)return t.pathname===`/${a.id}/vless`?new Response(b(e.headers.get("Host")),{status:200,headers:{"Content-Type":"text/plain;charset=utf-8"}}):new Response("Expected WebSocket",{status:400});const s=e.headers.get("sec-websocket-protocol"),n=m(s);if(g(new Uint8Array(n.slice(1,17)))!==a.uuid)return new Response("Invalid UUID",{status:403});const{0:o,1:i}=new WebSocketPair;return i.accept(),await(async()=>{try{const{tcpSocket:e,initialData:t}=await u(n);l(i,e,t);return new Response(null,{status:101,webSocket:o})}catch{return i.close(),new Response("Connection failed",{status:500})}})()}};function m(e){return Uint8Array.from(atob(e.replace(/-/g,"+").replace(/_/g,"/")),e=>e.charCodeAt(0)).buffer}function g(e){const t=Array.from(e,e=>e.toString(16).padStart(2,"0")).join("");return`${t.slice(0,8)}-${t.slice(8,12)}-${t.slice(12,16)}-${t.slice(16,20)}-${t.slice(20)}`}async function u(e){const t=new DataView(e),r=new Uint8Array(e)[17],s=18+r+1,n=t.getUint16(s),o=new Uint8Array(e)[s+2];let i=s+3,f="";if(1===o)f=Array.from(new Uint8Array(e.slice(i,i+4))).join("."),i+=4;else if(2===o){const t=new Uint8Array(e)[i];f=new TextDecoder().decode(e.slice(i+1,i+1+t)),i+=t+1}else{const t=[];for(let r=0;r<8;r++)t.push(t.getUint16(i+2*r).toString(16));f=t.join(":"),i+=16}const d=e.slice(i);async function h(e,t){const r=c({hostname:e,port:t});return await r.opened,r}try{if(a.enableSocks5Proxy&&a.enableSocks5Global)return console.log("尝试全局 SOCKS5 反代"),{tcpSocket:await p(o,f,n),initialData:d};return console.log(`尝试直连: ${f}:${n}`),{tcpSocket:await h(f,n),initialData:d}}catch(e){if(console.error("直连失败:",e),a.enableSocks5Proxy&&!a.enableSocks5Global)return console.log("尝试非全局 SOCKS5 反代"),{tcpSocket:await p(o,f,n),initialData:d};if(a.enableProxy&&a.proxyIP){console.log("尝试 proxyIP 反代:",a.proxyIP);const[e,t]=a.proxyIP.split(":");return{tcpSocket:await h(e,Number(t)||n),initialData:d}}throw new Error("All connection attempts failed")}}async function l(e,t,r){e.send(new Uint8Array([0,0]));const s=t.writable.getWriter(),n=t.readable.getReader();r&&await s.write(r),e.addEventListener("message",e=>s.write(e.data));try{for(;;){const{value:t,done:r}=await n.read();if(r)break;e.send(t)}}catch{}finally{e.close();try{await n.cancel()}catch{}s.releaseLock(),t.close()}}async function p(e,t,r){console.log(`尝试连接 SOCKS5: ${a.socks5Account}`);const{s:n,d:o,h:i,p:f}=await y(a.socks5Account);console.log(`SOCKS5 参数: hostname=${i}, port=${f}, username=${n||"无"}`);const m=c({hostname:i,port:f});try{await Promise.race([m.opened,new Promise((e,t)=>setTimeout(()=>t(new Error("SOCKS5 连接超时")),5e3))]),console.log("SOCKS5 连接成功")}catch(e){throw console.error("SOCKS5 连接失败:",e),new Error(`SOCKS5 连接失败: ${e.message}`)}const g=m.writable.getWriter(),u=m.readable.getReader(),l=new TextEncoder,h=new Uint8Array([5,2,0,2]);await g.write(h);let b=(await u.read()).value;if(2===b[1]){if(!n||!o)throw new Error("SOCKS5 认证失败: 缺少用户名或密码");const e=new Uint8Array([1,n.length,...l.encode(n),o.length,...l.encode(o)]);await g.write(e),b=(await u.read()).value;if(1!==b[0]||0!==b[1])throw new Error("SOCKS5 认证失败: 无效响应")}let w;switch(e){case 1:w=new Uint8Array([1,...t.split(".").map(Number)]);break;case 2:w=new Uint8Array([3,t.length,...l.encode(t)]);break;case 3:w=new Uint8Array([4,...t.split(":").flatMap(e=>[parseInt(e.slice(0,2),16),parseInt(e.slice(2),16)])]);break;default:throw new Error("无效地址类型")}const S=new Uint8Array([5,1,0,...w,r>>8,r&255]);await g.write(S),b=(await u.read()).value;if(5!==b[0]||0!==b[1])throw new Error("SOCKS5 请求失败");return g.releaseLock(),u.releaseLock(),m}async function y(e){const t=e.replace(/^socks5:\/\//,"").split("@").reverse(),[r,s]=t[1]?.split(":")||[],n=t[0].split(":"),o=Number(n.pop());return{h:n.join(":"),p:o,s:r,d:s}}function b(e){return`vless://${a.uuid}@${a.node}:443?encryption=none&security=tls&sni=${e}&type=ws&host=${e}&path=${encodeURIComponent("/?ed=2560")}#${encodeURIComponent(a.nodeName)}`}
