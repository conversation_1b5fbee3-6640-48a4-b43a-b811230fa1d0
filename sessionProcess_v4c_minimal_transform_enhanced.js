// 版本4c：极简版 TransformStream 增强变体 - 更接近原版本结构
// 使用 TransformStream 但保持原版本的处理逻辑和错误处理

export async function sessionProcess_v4c_minimal_transform_enhanced(request, env, ctx, protocolMode) {
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";
    let tcpInterface = null;
    let writer = null;

    // 使用 TransformStream 作为上游缓冲区（类似原版本的 hold）
    const hold = new TransformStream();
    const upstreamReadable = hold.readable;
    const holdWriter = hold.writable.getWriter();

    // 处理早期数据
    if (earlyHeader) {
        try {
            holdWriter.write(decodeBase64Url(earlyHeader)).catch(() => {});
        } catch (e) {}
    }

    // WebSocket 事件处理
    server.addEventListener("message", e => {
        holdWriter.write(e.data).catch(() => {});
    });

    server.addEventListener("close", () => {
        try {
            holdWriter.close().catch(() => {});
        } catch (e) {}
        try { 
            tcpInterface?.close(); 
        } catch (e) {}
    });

    server.addEventListener("error", () => {
        try {
            holdWriter.abort().catch(() => {});
        } catch (e) {}
        try { 
            tcpInterface?.close(); 
        } catch (e) {}
        try { 
            server.close(1013); 
        } catch (e) {}
    });

    (async () => {
        try {
            // 解析头部（固定使用 reader 模式）
            const header = await parseHeader(upstreamReadable, server, protocolMode, 'reader');
            
            // 连接逻辑
            try {
                tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
            } catch {
                tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
            }

            writer = tcpInterface.writable.getWriter();

            // 上游处理：使用 reader 模式（性能最优）
            const upstreamReader = upstreamReadable.getReader();
            const upstreamTask = (async () => {
                try {
                    for (;;) {
                        const { value: chunk, done } = await upstreamReader.read();
                        if (done) break;
                        await writer.write(chunk);
                    }
                } finally {
                    upstreamReader.releaseLock();
                    writer.close().catch(() => {});
                }
            })();

            // 写入初始数据
            if (header.rawClientData) {
                await writer.write(header.rawClientData);
            }

            // 下游处理：使用 reader 模式（性能最优）
            const tcpReader = tcpInterface.readable.getReader();
            try {
                for (;;) {
                    const { value: chunk, done } = await tcpReader.read();
                    if (done) break;
                    server.send(chunk);
                }
            } finally {
                tcpReader.releaseLock();
            }

            // 等待上游任务完成
            await upstreamTask;

        } catch (e) {
            try { tcpInterface?.close(); } catch (e) {}
        } finally {
            try { tcpInterface?.close(); } catch (e) {}
            try { server.close(1000); } catch (e) {}
        }
    })().catch(() => {
        try { tcpInterface?.close(); } catch (e) {}
        try { server.close(1000); } catch (e) {}
    });

    return new Response(null, { status: 101, webSocket: client });
}

// 如果要替换原函数，取消下面的注释：
// export { sessionProcess_v4c_minimal_transform_enhanced as sessionProcess };
