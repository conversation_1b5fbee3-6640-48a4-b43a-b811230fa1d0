// 版本4b：极简版 TransformStream 变体 - 使用 TransformStream 替代 ReadableStream
// 基于版本4，改用 TransformStream 方式处理 WebSocket 流

export async function sessionProcess_v4b_minimal_transform(request, env, ctx, protocolMode) {
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";
    let tcpInterface = null;

    // 使用 TransformStream 作为 WebSocket 流缓冲区
    const wsTransform = new TransformStream();
    const wsStream = wsTransform.readable;
    const wsWriter = wsTransform.writable.getWriter();

    // 处理早期数据
    if (earlyHeader) {
        try {
            wsWriter.write(decodeBase64Url(earlyHeader)).catch(() => {});
        } catch (e) {}
    }

    // WebSocket 消息处理
    server.addEventListener("message", e => {
        wsWriter.write(e.data).catch(() => {});
    });

    server.addEventListener("close", () => {
        try {
            wsWriter.close().catch(() => {});
        } catch (e) {}
        try { 
            tcpInterface?.close(); 
        } catch (e) {}
    });

    server.addEventListener("error", () => {
        try {
            wsWriter.abort().catch(() => {});
        } catch (e) {}
        try { 
            tcpInterface?.close(); 
        } catch (e) {}
        try { 
            server.close(1013); 
        } catch (e) {}
    });

    (async () => {
        try {
            // 解析头部
            const header = await parseHeader(wsStream, server, protocolMode, 'reader');
            
            // 连接（简单重试）
            try {
                tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
            } catch {
                tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
            }

            // 双向数据传输
            const writer = tcpInterface.writable.getWriter();
            const reader = tcpInterface.readable.getReader();

            // 上游：WS → TCP
            wsStream.pipeTo(new WritableStream({
                write: chunk => writer.write(chunk),
                close: () => writer.close()
            })).catch(() => {});

            // 写入初始数据
            if (header.rawClientData) {
                writer.write(header.rawClientData).catch(() => {});
            }

            // 下游：TCP → WS
            try {
                for (;;) {
                    const { value, done } = await reader.read();
                    if (done) break;
                    server.send(value);
                }
            } finally {
                reader.releaseLock();
            }

        } catch (e) {
            tcpInterface?.close();
        } finally {
            tcpInterface?.close();
            server.close(1000);
        }
    })().catch(() => {
        tcpInterface?.close();
        server.close(1000);
    });

    return new Response(null, { status: 101, webSocket: client });
}

// 如果要替换原函数，取消下面的注释：
// export { sessionProcess_v4b_minimal_transform as sessionProcess };
